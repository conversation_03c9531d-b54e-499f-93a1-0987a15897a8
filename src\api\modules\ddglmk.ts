/*
 * @OriginalName: 订单管理模块
 * @Description: 订单的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 审核订单
 * @description 按 Order的Id，审核订单，conclusion可以是“订单完成”或“退回”。
 */
export function verifyOrderById_02(params?: { orderId: number; conclusion: string }) {
  return request<R>(`/order/verifyOrderById`, {
    method: 'post',
    params,
  });
}

/**
 * 更新订单
 * @description 按orderDTO的信息，更新订单的信息。
 */
export function updateOrder(data: UpdateOrderDTO) {
  return request<ROrderVO>(`/order/updateOrder`, {
    method: 'post',
    data,
  });
}

/**
 * 新建订单
 * @description 按orderDTO的信息，新建订单的信息。
 */
export function newOrder(data: NewOrderDTO) {
  return request<ROrderVO>(`/order/newOrder`, {
    method: 'post',
    data,
  });
}

/**
 * 新建或更新订单明细
 * @description 按orderDTO的信息，新建或更新订单明细的信息。
 */
export function newOrUpdateOrderItem_1(data: Array<NewOrderItemDTO>, params?: { orderId: number }) {
  return request<RListOrderItemVO>(`/order/newOrUpdateOrderItem`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 查找订单
 * @description 按动态条件，获取满足相应条件的订单的基本信息。各条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findOrderVOByCriteria(data: OrderCriteria) {
  return request<REntityVOPage>(`/order/findProductVOByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 查找订单明细
 * @description 按订单明细的Id，精确查找数据记录。
 */
export function findAllOrderItemById(data: Array<OrderItemId>) {
  return request<RListOrderItemVO>(`/order/findAllOrderItemById`, {
    method: 'post',
    data,
  });
}

/**
 * 删除订单明细
 * @description 按 Order的Id，删除一个或多个订单明细的记录。
 */
export function deleteOrderItemById(data: Array<OrderItemId>) {
  return request<R>(`/order/deleteOrderItemById`, {
    method: 'post',
    data,
  });
}

/**
 * 提交订单
 * @description 提交订单。
 */
export function submitOrder(orderId: number, params?: { orderId: number }) {
  return request<ROrderVO>(`/order/submitOrder/${orderId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找订单处理记录
 * @description 按用户的Id，查询订单处理的全部记录。
 */
export function findUserOrderProcessingByUserId(
  userId: number,
  pageNum: number,
  pageSize: number,
  params?: { userId: number; pageNum: number; pageSize: number }
) {
  return request<RVOPage>(`/order/findUserOrderProcessingByUserId/${userId}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找订单处理记录
 * @description 按订单的Id，查询订单处理的全部记录。
 */
export function findUserOrderProcessingByOrderId(
  orderId: number,
  pageNum: number,
  pageSize: number,
  params?: { orderId: number; pageNum: number; pageSize: number }
) {
  return request<REntityVOPage>(`/order/findUserOrderProcessingByOrderId/${orderId}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找订单明细
 * @description 按订单的Id，查询订单的明细。
 */
export function findOrderItemVOByOrderId(orderId: number, params?: { orderId: number }) {
  return request<RListOrderItemVO>(`/order/findOrderItemVOByOrderId/${orderId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找用户的采购订单
 * @description 按用户的Id，查询用户的采购订单。
 */
export function findOrderByUserId(
  userId: number,
  pageNum: number,
  pageSize: number,
  params?: { userId: number; pageNum: number; pageSize: number }
) {
  return request<REntityVOPage>(`/order/findOrderByUserId/${userId}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找订单
 * @description 按Order的Id，精确查找数据记录。
 */
export function findOrderById(orderId: Array<number>, params?: { orderId: Array<number> }) {
  return request<RListOrderVO>(`/order/findOrderById/${orderId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找订单的发票
 * @description 按订单的Id，查询订单的发票。
 */
export function findInvoiceVOByOrderId(orderId: number, params?: { orderId: number }) {
  return request<RListInvoiceVO>(`/order/findInvoiceVOByOrderId/${orderId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找用户订单处理
 * @description 按订单处理的Id，精确查找数据记录。
 */
export function findAllOrderProcessingById(
  orderProcessingId: Array<number>,
  params?: { orderProcessingId: Array<number> }
) {
  return request<RListUserOrderProcessingVO>(`/order/findAllOrderProcessingById/${orderProcessingId}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页显示全部订单
 * @description 分页显示全部订单
 */
export function findAllOrder(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<REntityVOPage>(`/order/findAllOrder/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除订单
 * @description 按 Order的Id，删除一个“新建”的订单的记录。
 */
export function deleteOrderById(orderId: number, params?: { orderId: number }) {
  return request<R>(`/order/deleteOrderById/${orderId}`, {
    method: 'get',
    params,
  });
}
