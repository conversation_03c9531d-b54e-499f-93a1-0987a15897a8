export default {
  '/order/verifyOrderById': 'verifyOrderById_02',
  '/order/updateOrder': 'updateOrder',
  '/order/newOrder': 'newOrder',
  '/order/newOrUpdateOrderItem': 'newOrUpdateOrderItem_1',
  '/order/findProductVOByCriteria': 'findOrderVOByCriteria',
  '/order/findAllOrderItemById': 'findAllOrderItemById',
  '/order/deleteOrderItemById': 'deleteOrderItemById',
  '/order/submitOrder/{orderId}': 'submitOrder',
  '/order/findUserOrderProcessingByUserId/{userId}/{pageNum}/{pageSize}': 'findUserOrderProcessingByUserId',
  '/order/findUserOrderProcessingByOrderId/{orderId}/{pageNum}/{pageSize}': 'findUserOrderProcessingByOrderId',
  '/order/findOrderItemVOByOrderId/{orderId}': 'findOrderItemVOByOrderId',
  '/order/findOrderByUserId/{userId}/{pageNum}/{pageSize}': 'findOrderByUserId',
  '/order/findOrderById/{orderId}': 'findOrderById',
  '/order/findInvoiceVOByOrderId/{orderId}': 'findInvoiceVOByOrderId',
  '/order/findAllOrderProcessingById/{orderProcessingId}': 'findAllOrderProcessingById',
  '/order/findAllOrder/{pageNum}/{pageSize}': 'findAllOrder',
  '/order/deleteOrderById/{orderId}': 'deleteOrderById',
  '/rolePrivilege/assignPrivilege2': 'assignPrivilege2',
  '/rolePrivilege/assignPrivilege1': 'assignPrivilege1',
  '/rolePrivilege/findRolePrivilegeByRoleId/{roleId}': 'findRolePrivilegeByRoleIdVO',
  '/rolePrivilege/deleteRolePrivilege/{roleId}': 'deleteRolePrivilege_02',
  '/userRole/assignRole2': 'assignRole2',
  '/userRole/findUserRoleVOByUserIdVO/{userId}': 'findUserRoleVOByUserIdVO',
  '/userRole/deleteUserRole/{userId}': 'deleteUserRole_02',
  '/role/newOrUpdateEntity': 'newOrUpdateEntity_2_02',
  '/role/findEntityByIdCollection': 'findEntityById_2_02',
  '/role/deleteEntityByIdCollection': 'deleteEntityById_2_02',
  '/role/searchRoles/{pageNum}/{pageSize}': 'searchRoles',
  '/role/findRolesByType': 'findRolesByType',
  '/role/findEntityByIdArray/{idArray}': 'findEntityById_23_02',
  '/role/findEntityById/{id}': 'findEntityById_24_02',
  '/role/findAll/{pageNum}/{pageSize}': 'findAll_2_02',
  '/role/deleteEntityByIdArray/{idArray}': 'deleteEntityById_23_02',
  '/role/deleteEntityById/{id}': 'deleteEntityById_24_02',
  '/OriginalSheet/newOrUpdateEntity': 'newOrUpdateEntity_10_02',
  '/OriginalSheet/findEntityByIdCollection': 'findEntityById_10_02',
  '/OriginalSheet/deleteEntityByIdCollection': 'deleteEntityById_10_02',
  '/OriginalSheet/publishOriginalSheetById/{originalSheetId}': 'publishOriginalSheetById',
  '/OriginalSheet/findEntityByIdArray/{idArray}': 'findEntityById_37_02',
  '/OriginalSheet/findEntityById/{id}': 'findEntityById_38_02',
  '/OriginalSheet/findAll/{pageNum}/{pageSize}': 'findAll_10_02',
  '/OriginalSheet/deleteEntityByIdArray/{idArray}': 'deleteEntityById_37_02',
  '/OriginalSheet/deleteEntityById/{id}': 'deleteEntityById_38_02',
  '/OriginalSheet/clearOriginalSheetAndDatasetById/{originalSheetId}': 'clearOriginalSheetAndDatasetById',
  '/OriginalColumn/newOrUpdateEntity': 'newOrUpdateEntity_11_02',
  '/OriginalColumn/findEntityByIdCollection': 'findEntityById_11_02',
  '/OriginalColumn/deleteEntityByIdCollection': 'deleteEntityById_11_02',
  '/OriginalColumn/findEntityByIdArray/{idArray}': 'findEntityById_39_02',
  '/OriginalColumn/findEntityById/{id}': 'findEntityById_40_02',
  '/OriginalColumn/findAll/{pageNum}/{pageSize}': 'findAll_11_02',
  '/OriginalColumn/deleteEntityByIdArray/{idArray}': 'deleteEntityById_39_02',
  '/OriginalColumn/deleteEntityById/{id}': 'deleteEntityById_40_02',
  '/privilege/newOrUpdateEntity': 'newOrUpdateEntity_4_02',
  '/privilege/findEntityByIdCollection': 'findEntityById_4_02',
  '/privilege/deleteEntityByIdCollection': 'deleteEntityById_4_02',
  '/privilege/searchPrivileges/{pageNum}/{pageSize}': 'searchPrivileges',
  '/privilege/findPrivilegesByType': 'findPrivilegesByType',
  '/privilege/findEntityByIdArray/{idArray}': 'findEntityById_27_02',
  '/privilege/findEntityById/{id}': 'findEntityById_28_02',
  '/privilege/findAll/{pageNum}/{pageSize}': 'findAll_4_02',
  '/privilege/deleteEntityByIdArray/{idArray}': 'deleteEntityById_27_02',
  '/privilege/deleteEntityById/{id}': 'deleteEntityById_28_02',
  '/userEnrollment/newOrUpdateEntity': 'newOrUpdateEntity',
  '/userEnrollment/findUserEnrollmentByCriteria': 'findUserEnrollmentByCriteria',
  '/userEnrollment/findEntityByIdCollection': 'findEntityById',
  '/userEnrollment/deleteEntityByIdCollection': 'deleteEntityById',
  '/userEnrollment/findEntityByIdArray/{idArray}': 'findEntityById_15',
  '/userEnrollment/findEntityById/{id}': 'findEntityById_16',
  '/userEnrollment/findAll/{pageNum}/{pageSize}': 'findAll',
  '/userEnrollment/deleteEntityByIdArray/{idArray}': 'deleteEntityById_15',
  '/userEnrollment/deleteEntityById/{id}': 'deleteEntityById_16',
  '/user/verifyEnroll': 'verifyEnroll',
  '/user/userEnroll': 'userEnroll',
  '/user/updatePassword': 'updatePassword',
  '/user/newOrUpdateUser': 'newOrUpdateUser',
  '/user/findUserVOByCriteria': 'findUserVOByCriteria',
  '/user/userInforSecurity/{userName}': 'getUserInforSecurity',
  '/user/userInfor/{userName}': 'getUserInfor',
  '/user/unlockMDMUserById/{userId}': 'unlockMDMUserById',
  '/user/summitEnrollAgain/{userId}': 'summitEnrollAgain',
  '/user/lockMDMUserById/{userId}': 'lockMDMUserById',
  '/user/findVerifyResult/{userId}': 'findVerifyResult',
  '/user/findUserEnrollmentByUserId/{userId}/{resolutionType}/{pageNum}/{pageSize}': 'findUserEnrollmentByUserId',
  '/user/findUserByOrgId/{userId}': 'findOrgUserByUserId',
  '/user/findTeamUserRoleVOByUserId/{userId}': 'findTeamUserRoleVOByUserId',
  '/user/findPaymentByUserId/{userId}/{pageNum}/{pageSize}': 'findPaymentByUserId',
  '/user/findEnrollVerify/{userType}/{pageNum}/{pageSize}': 'findEnrollVerify',
  '/user/findEnrollProcessing/{userType}/{pageNum}/{pageSize}': 'findEnrollProcessing',
  '/user/findEnrollProcessVerify/{userType}/{pageNum}/{pageSize}': 'findEnrollProcessVerify',
  '/user/findCBDDefTableByUserId/{userId}': 'findCBDDefTableByUserId',
  '/user/findAllUserPagable/{pageNum}/{pageSize}': 'findAllUser',
  '/user/deleteMDMUserById/{userId}': 'deletePrivilegeById',
  '/annotation/findannotationByCriteria': 'findannotationByCriteria',
  '/annotation/findTotalAmountByDictionaryCodeOrg': 'findTotalAmountByDictionaryCodeOrg',
  '/annotation/findTotalAmountByDictionaryCodeDisease': 'findTotalAmountByDictionaryCodeDisease',
  '/annotation/findTotalAmountByAnnotationId/{dictionaryCode}': 'findTotalAmountBySystemDictionaryCode',
  '/invoice/newOrUpdateProduct': 'newOrUpdateOrg_1',
  '/invoice/findInvoiceByCriteria': 'findInvoiceByCriteria',
  '/invoice/findAllInvoiceById/{processId}': 'findAllInvoiceById',
  '/invoice/findAllInvoice/{pageNum}/{pageSize}': 'findAllInvoice',
  '/invoice/deleteProductById/{productId}': 'deleteProductById',
  '/process/newOrUpdateProcess': 'newOrUpdateProcess',
  '/process/findProcessByCriteria': 'findProcessByCriteria',
  '/process/findAllProcessById/{processId}': 'findAllProcessById',
  '/process/findAllProcess/{pageNum}/{pageSize}': 'findAllProcess',
  '/process/deleteProcessById/{processId}': 'deleteProcessById',
  '/org/removeUserFromOrg': 'deleteOrgUserById',
  '/org/newOrUpdateOrg': 'newOrUpdateOrg',
  '/org/findOrgVOByCriteria': 'findOrgVOByCriteria_1',
  '/org/addUserToOrg': 'newOrUpdateOrgUser',
  '/org/findUserByOrgId/{orgId}': 'findUserByOrgId',
  '/org/findTeamByOrgId/{orgId}': 'findTeamByOrgId',
  '/org/findOrgById/{orgId}': 'findOrgById',
  '/org/findAllOrgById/{orgId}': 'findAllOrgById',
  '/org/findAllOrg/{pageNum}/{pageSize}': 'findAllOrg',
  '/org/deleteOrgById/{orgId}': 'deleteOrgById',
  '/Directory/newOrUpdateEntity': 'newOrUpdateEntity_11',
  '/Directory/findEntityByIdCollection': 'findEntityById_11',
  '/Directory/deleteEntityByIdCollection': 'deleteEntityById_11',
  '/Directory/getTopDirectory': 'getTopDirectory',
  '/Directory/findEntityByIdArray/{idArray}': 'findEntityById_37',
  '/Directory/findEntityById/{id}': 'findEntityById_38',
  '/Directory/findAll/{pageNum}/{pageSize}': 'findAll_11',
  '/Directory/deleteEntityByIdArray/{idArray}': 'deleteEntityById_37',
  '/Directory/deleteEntityById/{id}': 'deleteEntityById_38',
  '/dataSource/testDataSource': 'testDataSource',
  '/catalogue/unmountMedicalFieldFromCatalogue': 'unmountMedicalFieldFromCatalogue',
  '/catalogue/newOrUpdateCatalogueVo': 'newOrUpdateCatalogue',
  '/catalogue/mountMedicalFieldToCatalogue': 'mountMedicalFieldToCatalogue',
  '/catalogue/{id}': 'getCatalogue',
  '/catalogue/topBasicCatalogues': 'getTopBasicCatalogues',
  '/catalogue/medicalFieldInCatalogue/{id}': 'getMedicalFieldInCatalogue',
  '/catalogue/getParentCatalogue/{id}': 'getParentCatalogue',
  '/catalogue/getChildCatalogue/{id}': 'getChildCatalogue',
  '/catalogue/getCatalogueByType': 'getCatalogueByType',
  '/catalogue/getCatalogueByTitle/{title}': 'getCatalogueByTitle',
  '/catalogue/getBrotherCatalogue/{id}': 'getBrotherCatalogue',
  '/catalogue/deleteCatalogue/{id}/{isObligated}': 'deleteCatalogue',
  '/catalogue/dataBrowse/chart': 'dataBrowse',
  '/dataSource/newOrUpdateDatabase': 'newOrUpdateDatabaseVO',
  '/dataSource/addTable': 'addTable',
  '/dataSource/addField': 'addField',
  '/dataSource/synchronizeVolume/{dbId}': 'synchronizeVolume',
  '/dataSource/findDBVOByDbId/{dbId}': 'findDBVOByDbId',
  '/dataSource/findAllTableVOByDbId/{dbId}': 'findAllTableVOByDbId',
  '/dataSource/findAllFieldVOByDbIdTblId/{dbId}/{tblId}': 'findAllFieldVOByDbIdTblId',
  '/dataSource/findAllDb': 'findAllDb',
  '/dataSource/deleteTableById/{id}/{isObligated}': 'deleteTableById',
  '/dataSource/deleteFieldById/{id}': 'deleteFieldById',
  '/dataSource/deleteDatabaseById/{id}/{isObligated}': 'deleteDatabaseById',
  '/dataSource/checkConnectionDBVOByDbId/{dbId}': 'checkConnectionDBVOByDbId',
  '/medicalData/uploadOriginalDocument/{orginalDocumentId}': 'uploadDocument',
  '/medicalData/newOrUpdateMedicalFieldVO': 'newOrUpdateMedicalFieldVO',
  '/medicalData/newOrUpdateAlternative/{mfId}': 'newOrUpdateAlternative',
  '/medicalData/findMedicalFieldsByFileInforIdAndDynamicConditions/{fileInforId}':
    'findMedicalFieldsByFileInforIdAndDynamicConditions',
  '/medicalData/findMedicalFieldVOByCriteria': 'findMedicalFieldVOByCriteria',
  '/medicalData/addStatisticTxtMedicalFieldId/{medicalFieldId}': 'addStatisticTxtMedicalFieldId',
  '/medicalData/addStatisticLongIntMedicalFieldId/{medicalFieldId}': 'addStatisticLongIntMedicalFieldId',
  '/medicalData/addStatisticFloatMedicalFieldId/{medicalFieldId}': 'addStatisticFloatMedicalFieldId',
  '/medicalData/addStatisticDateTimeMedicalFieldId/{medicalFieldId}': 'addStatisticDateTimeMedicalFieldId',
  '/medicalData/addStatisticCategoricalMedicalFieldId/{medicalFieldId}': 'addStatisticCategoricalMedicalFieldId',
  '/medicalData/addPublication/{mfId}': 'addPublication',
  '/medicalData/addOriginalDocument/{mfId}': 'addOriginalDocument',
  '/medicalData/{id}': 'getMedicalField',
  '/medicalData/synchronizeRecordCount/{medicalFieldId}': 'synchronizeRecordCount',
  '/medicalData/removeDocument/{orginalDocumentId}': 'removeDocument',
  '/medicalData/getProbabilityDistribution/{medicalFieldId}': 'getProbabilityDistribution',
  '/medicalData/findStatisticByMedicalFieldId/{medicalFieldId}': 'findStatisticByMedicalFieldId',
  '/medicalData/findPublicationByMedicalFieldId/{mfId}': 'findPublicationByMedicalFieldId',
  '/medicalData/findPlatStatistic': 'findPlatStatistic',
  '/medicalData/findOriginalDocumentByMedicalFieldId/{mfId}': 'findOriginalDocumentByMedicalFieldId',
  '/medicalData/findMedicalFieldVOByValueType/{mdfId}': 'findDataSourceById',
  '/medicalData/findFileInforByMedicalFieldId/{medicalFieldId}': 'findFileInforByMedicalFieldId',
  '/medicalData/findCatalogueVoBymdfId/{mdfId}': 'findCatalogueVoBymdfId',
  '/medicalData/findCBDDefFieldByMfId/{mfId}': 'findCBDDefFieldByMfId',
  '/medicalData/findAlternativeByMedicalFieldId/{mfId}': 'findAlternativeByMedicalFieldId',
  '/medicalData/disassociateCBDField/{mfId}/{cfdFieldId}': 'disassociateCBDField',
  '/medicalData/detail/{id}': 'getMedicalFieldDetail',
  '/medicalData/deleteStatistic/{statisticId}': 'deleteStatistic',
  '/medicalData/deletePublication/{mfId}/{pubId}': 'deletePublication',
  '/medicalData/deleteOriginalDocument/{mfId}/{ordId}': 'deleteOriginalDocument',
  '/medicalData/deleteMedicalFieldById/{mfId}': 'deleteMedicalFieldById',
  '/medicalData/deleteAlternative/{mfId}/{altId}': 'deleteAlternative',
  '/medicalData/associateCBDField/{mfId}/{cfdFieldId}': 'associateCBDField',
  '/team/removeUserFromTeam': 'deleteTeamUserById',
  '/team/removeTeamFromOrg/{orgId}/{teamId}': 'removeTeamFromOrg',
  '/team/newOrUpdateTeam': 'newOrUpdateTeam',
  '/team/findOrgVOByCriteria': 'findOrgVOByCriteria',
  '/team/addUserToTeam': 'newOrUpdateTeamUser',
  '/team/addTeamToOrg/{orgId}': 'addTeamToOrg',
  '/team/addTeamToOrg/{orgId}/{teamId}': 'addTeamToOrg_1',
  '/team/findUserByIdNotIn': 'findUserByIdNotIn_02',
  '/team/findTeamUserRoleVOByTeamId/{teamId}': 'findTeamUserRoleVOByTeamId',
  '/team/findTeamById/{teamId}': 'findTeamById',
  '/team/findApplicationTeamRoleVOByTeamId/{teamId}': 'findApplicationTeamRoleVOByTeamId',
  '/team/findAllTeam': 'findAllTeam_02',
  '/team/findAllTeamById': 'findAllTeamById_02',
  '/team/findAllApplicationByTeamId/{teamId}': 'findAllApplicationByTeamId',
  '/team/deleteTeamById/{teamId}': 'deleteTeamById',
  '/CommonFile/uploadFile': 'uploadFile_12',
  '/CommonFile/uploadFile/{fileId}': 'uploadFile_13',
  '/CommonFile/newOrUpdateEntity': 'newOrUpdateEntity_14',
  '/CommonFile/findEntityByIdCollection': 'findEntityById_14',
  '/CommonFile/deleteEntityByIdCollection': 'deleteEntityById_14',
  '/CommonFile/findEntityByIdArray/{idArray}': 'findEntityById_43',
  '/CommonFile/findEntityById/{id}': 'findEntityById_44',
  '/CommonFile/findAll/{pageNum}/{pageSize}': 'findAll_14',
  '/CommonFile/deleteEntityByIdArray/{idArray}': 'deleteEntityById_43',
  '/CommonFile/deleteEntityById/{id}': 'deleteEntityById_44',
  '/applicationProcessing/newOrUpdateEntity': 'newOrUpdateEntity_9',
  '/applicationProcessing/findEntityByIdCollection': 'findEntityById_9',
  '/applicationProcessing/deleteEntityByIdCollection': 'deleteEntityById_9',
  '/applicationProcessing/findEntityByIdArray/{idArray}': 'findEntityById_33',
  '/applicationProcessing/findEntityById/{id}': 'findEntityById_34',
  '/applicationProcessing/findAll/{pageNum}/{pageSize}': 'findAll_9',
  '/applicationProcessing/deleteEntityByIdArray/{idArray}': 'deleteEntityById_33',
  '/applicationProcessing/deleteEntityById/{id}': 'deleteEntityById_34',
  '/sourceCode/uploadFile': 'uploadFile',
  '/sourceCode/uploadFile/{sourceCodeId}': 'uploadFile_1',
  '/sourceCode/newOrUpdateEntity': 'newOrUpdateEntity_1',
  '/sourceCode/findEntityByIdCollection': 'findEntityById_1',
  '/sourceCode/deleteEntityByIdCollection': 'deleteEntityById_1',
  '/publicationreport/uploadFile': 'uploadFile_2',
  '/publicationreport/uploadFile/{publicationReportId}': 'uploadFile_3',
  '/publicationreport/newOrUpdateEntity': 'newOrUpdateEntity_2',
  '/publicationreport/findEntityByIdCollection': 'findEntityById_2',
  '/publicationreport/deleteEntityByIdCollection': 'deleteEntityById_2',
  '/manuscript/uploadFile': 'uploadFile_4',
  '/manuscript/uploadFile/{manuscriptId}': 'uploadFile_5',
  '/manuscript/newOrUpdateEntity': 'newOrUpdateEntity_4',
  '/manuscript/findEntityByIdCollection': 'findEntityById_4',
  '/manuscript/deleteEntityByIdCollection': 'deleteEntityById_4',
  '/datasetFile/uploadFile': 'uploadFile_6',
  '/datasetFile/uploadFile/{datasetFileId}': 'uploadFile_7',
  '/datasetFile/newOrUpdateEntity': 'newOrUpdateEntity_6',
  '/datasetFile/findEntityByIdCollection': 'findEntityById_6',
  '/datasetFile/deleteEntityByIdCollection': 'deleteEntityById_6',
  '/attachment/uploadFile': 'uploadFile_8',
  '/attachment/uploadFile/{attachmentId}': 'uploadFile_9',
  '/attachment/newOrUpdateEntity': 'newOrUpdateEntity_8',
  '/attachment/findEntityByIdCollection': 'findEntityById_8',
  '/attachment/deleteEntityByIdCollection': 'deleteEntityById_8',
  '/sourceCode/findEntityByIdArray/{idArray}': 'findEntityById_17',
  '/sourceCode/findEntityById/{id}': 'findEntityById_18',
  '/sourceCode/findAll/{pageNum}/{pageSize}': 'findAll_1',
  '/sourceCode/deleteEntityByIdArray/{idArray}': 'deleteEntityById_17',
  '/sourceCode/deleteEntityById/{id}': 'deleteEntityById_18',
  '/publicationreport/findEntityByIdArray/{idArray}': 'findEntityById_19',
  '/publicationreport/findEntityById/{id}': 'findEntityById_20',
  '/publicationreport/findAll/{pageNum}/{pageSize}': 'findAll_2',
  '/publicationreport/deleteEntityByIdArray/{idArray}': 'deleteEntityById_19',
  '/publicationreport/deleteEntityById/{id}': 'deleteEntityById_20',
  '/manuscript/findEntityByIdArray/{idArray}': 'findEntityById_23',
  '/manuscript/findEntityById/{id}': 'findEntityById_24',
  '/manuscript/findAll/{pageNum}/{pageSize}': 'findAll_4',
  '/manuscript/deleteEntityByIdArray/{idArray}': 'deleteEntityById_23',
  '/manuscript/deleteEntityById/{id}': 'deleteEntityById_24',
  '/datasetFile/findEntityByIdArray/{idArray}': 'findEntityById_27',
  '/datasetFile/findEntityById/{id}': 'findEntityById_28',
  '/datasetFile/findAll/{pageNum}/{pageSize}': 'findAll_6',
  '/datasetFile/deleteEntityByIdArray/{idArray}': 'deleteEntityById_27',
  '/datasetFile/deleteEntityById/{id}': 'deleteEntityById_28',
  '/attachment/findEntityByIdArray/{idArray}': 'findEntityById_31',
  '/attachment/findEntityById/{id}': 'findEntityById_32',
  '/attachment/findAll/{pageNum}/{pageSize}': 'findAll_8',
  '/attachment/deleteEntityByIdArray/{idArray}': 'deleteEntityById_31',
  '/attachment/deleteEntityById/{id}': 'deleteEntityById_32',
  '/enrollment/newOrUpdateEntity': 'newOrUpdateEntity_5',
  '/enrollment/findEntityByIdCollection': 'findEntityById_5',
  '/enrollment/findEnrollmentByCriteria': 'findEnrollmentByCriteria',
  '/enrollment/deleteEntityByIdCollection': 'deleteEntityById_5',
  '/enrollment/findUserEnrollmentByUserId/{enrollmentId}/{resolutionType}/{pageNum}/{pageSize}':
    'findUserEnrollmentByUserId_1',
  '/enrollment/findUserEnrollment/{enrollmentId}/{pageNum}/{pageSize}': 'findUserEnrollmentByUserId_2',
  '/enrollment/findEntityByIdArray/{idArray}': 'findEntityById_25',
  '/enrollment/findEntityById/{id}': 'findEntityById_26',
  '/enrollment/findAll/{pageNum}/{pageSize}': 'findAll_5',
  '/enrollment/deleteEntityByIdArray/{idArray}': 'deleteEntityById_25',
  '/enrollment/deleteEntityById/{id}': 'deleteEntityById_26',
  '/FileInfor/uploadFileId/{fileInforId}': 'uploadFile_10',
  '/FileInfor/uploadFileId/hasmdd/{userIdCur}/{cbdDatabaseId}': 'uploadFile_11_02',
  '/FileInfor/setFileInforAnnotation/{annotationId}': 'setFileInforAnnotation_02',
  '/FileInfor/newOrUpdateEntity': 'newOrUpdateEntity_10',
  '/FileInfor/getDataInventory': 'getDataInventory',
  '/FileInfor/findFileInforByUserId/{userId}/{pageNum}/{pageSize}': 'findFileInforByUserId',
  '/FileInfor/findFileInforByCriteria': 'findFileInforByCriteria',
  '/FileInfor/findFileInforByAnnotationId': 'findFileInforByAnnotationId',
  '/FileInfor/findEntityByIdCollection': 'findEntityById_10',
  '/FileInfor/exportMultiMedicalDataSetToDatabase/{fileId}': 'exportMultiMedicalDataSetToDatabase',
  '/FileInfor/exportMedicalDataSetToDatabase/{fileId}': 'exportMedicalDataSetToDatabase',
  '/FileInfor/deleteEntityByIdCollection': 'deleteEntityById_10',
  '/FileInfor/status': 'getThreadPoolStatus',
  '/FileInfor/setDatabase/{fileInforId}/{databaseId}': 'setDatabase',
  '/FileInfor/findMedicalFieldsByTableId': 'findMedicalFieldsByTableId',
  '/FileInfor/findHistoryFileByFileInforId/{fileInforId}': 'findHistoryFileByFileInforId',
  '/FileInfor/findEntityByIdArray/{idArray}': 'findEntityById_35',
  '/FileInfor/findEntityById/{id}': 'findEntityById_36',
  '/FileInfor/findAll/{pageNum}/{pageSize}': 'findAll_10',
  '/FileInfor/deleteEntityByIdArray/{idArray}': 'deleteEntityById_35',
  '/FileInfor/deleteEntityById/{id}': 'deleteEntityById_36',
  '/Dictionary/newOrUpdateEntity': 'newOrUpdateEntity_13',
  '/Dictionary/findEntityByIdCollection': 'findEntityById_13',
  '/Dictionary/deleteEntityByIdCollection': 'deleteEntityById_13',
  '/Dictionary/findEntityByIdArray/{idArray}': 'findEntityById_41',
  '/Dictionary/findEntityById/{id}': 'findEntityById_42',
  '/Dictionary/findByState/{pageNum}/{pageSize}': 'findByState_1',
  '/Dictionary/findAll/{pageNum}/{pageSize}': 'findAll_13',
  '/Dictionary/deleteEntityByIdArray/{idArray}': 'deleteEntityById_41',
  '/Dictionary/deleteEntityById/{id}': 'deleteEntityById_42',
  '/DictionaryValue/newOrUpdateEntity': 'newOrUpdateEntity_12',
  '/DictionaryValue/findEntityByIdCollection': 'findEntityById_12',
  '/DictionaryValue/deleteEntityByIdCollection': 'deleteEntityById_12',
  '/DictionaryValue/findEntityByIdArray/{idArray}': 'findEntityById_39',
  '/DictionaryValue/findEntityById/{id}': 'findEntityById_40',
  '/DictionaryValue/findByBictionaryCode/{pageNum}/{pageSize}': 'findByBictionaryCode',
  '/DictionaryValue/findAll/{pageNum}/{pageSize}': 'findAll_12',
  '/DictionaryValue/deleteEntityByIdArray/{idArray}': 'deleteEntityById_39',
  '/DictionaryValue/deleteEntityById/{id}': 'deleteEntityById_40',
  '/payment/newOrUpdateEntity': 'newOrUpdateEntity_3',
  '/payment/findPaymentByCriteria': 'findPaymentByCriteria',
  '/payment/findEntityByIdCollection': 'findEntityById_3',
  '/payment/deleteEntityByIdCollection': 'deleteEntityById_3',
  '/payment/generateQRCode': 'generateQRCode',
  '/payment/findUserByByPaymentId/{paymentId}': 'findUserByByPaymentId',
  '/payment/findPaymentByOrderId/{orderId}': 'findPaymentByOrderId_02',
  '/payment/findOrderByPaymentId/{paymentId}': 'findOrderByPaymentId',
  '/payment/findEntityByIdArray/{idArray}': 'findEntityById_21',
  '/payment/findEntityById/{id}': 'findEntityById_22',
  '/payment/findAll/{pageNum}/{pageSize}': 'findAll_3',
  '/payment/deleteEntityByIdArray/{idArray}': 'deleteEntityById_21',
  '/payment/deleteEntityById/{id}': 'deleteEntityById_22',
  '/attachmentMaterial/uploadAttachmentMaterial': 'uploadAttachmentMaterial',
  '/attachmentMaterial/uploadAttachmentMaterialId/{attachmentMaterialId}': 'uploadAttachmentMaterial_1',
  '/attachmentMaterial/newOrUpdateEntity': 'newOrUpdateEntity_7',
  '/attachmentMaterial/findEntityByIdCollection': 'findEntityById_7',
  '/attachmentMaterial/findAttachmentMaterialByCriteria': 'findAttachmentMaterialByCriteria',
  '/attachmentMaterial/deleteEntityByIdCollection': 'deleteEntityById_7',
  '/attachmentMaterial/removeAttachmentMaterial/{attachmentMaterialId}': 'removeAttachmentMaterial',
  '/attachmentMaterial/getAttachmentMaterialfile/{attachmentMaterialId}': 'getAttachmentMaterialfile',
  '/attachmentMaterial/findEntityByIdArray/{idArray}': 'findEntityById_29',
  '/attachmentMaterial/findEntityById/{id}': 'findEntityById_30',
  '/attachmentMaterial/findAll/{pageNum}/{pageSize}': 'findAll_7',
  '/attachmentMaterial/deleteEntityByIdArray/{idArray}': 'deleteEntityById_29',
  '/attachmentMaterial/deleteEntityById/{id}': 'deleteEntityById_30',
  '/teamUserRole/deleteTeamUserRole/{userId}': 'deleteTeamUserRole',
  '/teamUserRole/assignTeamUserRoles': 'assignTeamUserRoles',
  '/teamUserRole/findTeamUserRoleByTeamUserId': 'findTeamUserRoleByTeamUserId',
  '/application/verifyApplication': 'verifyApplication',
  '/application/updateApplication/{userId}': 'newOrUpdateApplication_02',
  '/application/newOrUpdateApplication/{userId}': 'newOrUpdateApplication_1',
  '/application/findApplicationVOByCriteria': 'findApplicationVOByCriteria',
  '/application/findVerifyResult/{applicationId}': 'findVerifyResult_1',
  '/application/findOrderByApplicationId/{applicationId}/{pageNum}/{pageSize}': 'findOrderByApplicationId',
  '/application/findFileInforByApplicationId/{applicationId}/{pageNum}/{pageSize}': 'findFileInforByApplicationId',
  '/application/findAppById/{appId}': 'findAppById',
  '/application/findAllVerifyApplicationByStateIn/{pageNum}/{pageSize}': 'findAllVerifyApplicationByStateIn',
  '/application/findAllVerifyApplication/{pageNum}/{pageSize}': 'findAllVerifyApplication',
  '/application/findAllApplicationById/{appId}': 'findAllApplicationById',
  '/application/findAllApplication/{pageNum}/{pageSize}': 'findAllApplication',
  '/application/deleteApplicationById/{applicationId}': 'deleteApplicationById',
  '/application/applyApplication/{applicationId}/{userId}': 'applyApplication',
  '/ApplicationUser/addApplicationUser': 'addApplicationUser',
  '/ApplicationUser/addApplicationUserDev': 'addApplicationUserDev',
  '/ApplicationUser/removeApplicationUser': 'deleteApplicationUser',
  '/ApplicationUser/removeApplicationUserRole': 'removeApplicationUserRole',
  '/ApplicationUser/removeApplicationUserRoleDev': 'removeApplicationUserRoleDev',
  '/ApplicationUser/findApplicationUserByMdmUserId': 'findApplicationUserByMdmUserId',
  '/ApplicationUser/findApplicationUserByApplicationId': 'findApplicationUserByApplicationId',
  '/ApplicationUser/findApplicationUserByApplicationIdAndMdmUserId': 'findApplicationUserByApplicationIdAndMdmUserId',
};
