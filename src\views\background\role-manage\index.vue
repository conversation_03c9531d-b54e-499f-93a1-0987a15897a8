<template>
  <div class="bg-baf flex h-full flex-col overflow-hidden">
    <div v-loading="loading" class="bg-w m-5 flex h-0 flex-1 flex-col rounded-md pt-5">
      <div class="flex gap-4 px-10">
        <!-- <el-input v-model="search" placeholder="角色名称" style="width: 200px" clearable @keyup.enter="onSearch" />
        <el-input v-model="search" placeholder="角色编码" style="width: 200px" clearable @keyup.enter="onSearch" /> -->
        <div>
          <!-- <el-button type="default" @click="onSearch">查询</el-button> -->
          <el-button type="primary" @click="onAdd">新增</el-button>
        </div>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column prop="roleName" label="角色名称" />
          <el-table-column prop="roleCode" label="角色编码" />
          <el-table-column prop="type" label="类型" width="100" />
          <!-- <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.rltSybole?.lockFlag === '0' ? 'success' : 'danger'">{{
                row.rltSybole?.lockFlag === '0' ? '启用' : '禁用'
              }}</el-tag>
            </template>
          </el-table-column> -->
          <el-table-column prop="note" label="备注" />
          <!-- <el-table-column prop="description" label="描述" show-overflow-tooltip /> -->
          <el-table-column prop="id" label="更新时间" width="180">
            <template #default="{ row }">
              {{ row.rltTime.updateTime }}
            </template>
          </el-table-column>
          <!-- <el-table-column prop="sort" label="排序" /> -->
          <el-table-column fixed="right" label="操作" width="80px">
            <template #default="{ row }">
              <el-tooltip content="编辑" effect="dark">
                <el-button link type="primary" icon="edit" @click="onEdit(row)" />
              </el-tooltip>
              <el-popconfirm v-if="!orginRoles.includes(row.roleCode)" title="确定删除？" @confirm="onDel(row)">
                <template #reference>
                  <span class="ml-3">
                    <el-tooltip content="删除" effect="dark">
                      <el-button link icon="delete" type="primary" />
                    </el-tooltip>
                  </span>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <el-dialog v-model="showAdd" :title="formTitle" width="600px" @close="onAddClose">
      <el-form ref="formRef" :model="addForm" :rules="rules" label-width="140px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="addForm.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色编码" prop="roleCode">
          <el-input v-model="addForm.roleCode" :disabled="addForm.id ? true : false" placeholder="请输入角色编码" />
        </el-form-item>
        <!-- <el-form-item label="状态" prop="status">
          <el-switch
            v-model="addForm.rltSybole!.lock_flag"
            active-value="0"
            inactive-value="1"
            active-text="启用"
            inactive-text="停用"
            inline-prompt
          >
          </el-switch>
        </el-form-item> -->
        <el-form-item label="类型" prop="type">
          <el-select v-model="addForm.type" placeholder="请输入类型">
            <el-option v-for="item in ['系统用户']" :key="item" :label="item" :value="item"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input v-model="addForm.note" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="addForm.description" placeholder="请输入描述" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span>
          <el-button @click="onAddClose">取消</el-button>
          <el-button type="primary" :loading="addLoading" @click="onAddConfirm"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { deleteEntityById_24_02, findAll_2_02, newOrUpdateEntity_2_02 } from '@/api';
  import { orginRoles } from '@/config/origin-role';
  import { ElMessage, FormInstance } from 'element-plus';

  const loading = ref(false);
  const search = ref('');
  const onSearch = () => {
    fetchData();
  };

  //---------新增角色-----------
  const formRef = ref<FormInstance>();
  const addForm = reactive<RoleVO>({
    id: 0,
    roleName: '',
    roleCode: '',
    description: '',
    note: '',
    rltSybole: {
      del_flag: '0',
      lock_flag: '0',
    },
    type: '',
  });
  const rules = ref({
    roleName: [{ required: true, message: '不能为空' }],
    roleCode: [{ required: true, message: '不能为空' }],
    type: [{ required: true, message: '不能为空' }],
    note: [{ required: true, message: '不能为空' }],
  });
  const showAdd = ref(false);
  const addLoading = ref(false);
  const formTitle = computed(() => (addForm.id ? '编辑角色' : '新增角色'));

  const onAdd = () => {
    addForm.id = 0;
    showAdd.value = true;
  };
  const onEdit = (row: any) => {
    showAdd.value = true;
    nextTick(() => {
      for (const key in addForm) {
        if (Object.prototype.hasOwnProperty.call(addForm, key) && key !== 'rltSybole') {
          addForm[key] = row[key];
        }
        // addForm.rltSybole!.lock_flag = row.rltSybole.lockFlag;
      }
    });
  };
  const onAddClose = () => {
    showAdd.value = false;
    formRef.value?.resetFields();
  };
  const onAddConfirm = () => {
    formRef.value?.validate(async (valid) => {
      try {
        if (valid) {
          addLoading.value = true;
          await newOrUpdateEntity_2_02(addForm);
          ElMessage({ type: 'success', message: '操作成功' });
          onAddClose();
          fetchData();
        }
      } catch (error) {
        console.log(error);
      } finally {
        addLoading.value = false;
      }
    });
  };

  //----------角色列表----------
  const tableData = ref<RoleVO[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      const { data } = await findAll_2_02(pageNum, pagination.pageSize);
      const anyData = data as any;
      total.value = anyData?.totalElement || 0;
      tableData.value = anyData?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };

  //删除
  async function onDel(row: RoleVO) {
    try {
      loading.value = true;
      await deleteEntityById_24_02(row.id!);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  onBeforeMount(() => {
    fetchData();
  });
</script>
