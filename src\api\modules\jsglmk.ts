/*
 * @OriginalName: 角色管理模块
 * @Description: 管理系统中的角色信息
 */
import { request } from '@/utils/request';

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_2_02(data: RoleDTO) {
  return request<RRoleVO>(`/role/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_2_02(data: Array<number>) {
  return request<RListRoleVO>(`/role/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_2_02(data: Array<number>) {
  return request<R>(`/role/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 分页查询角色记录
 * @description 按名字，代码或类型进行模糊查询，分页形式查询角色数据记录，若查询条件是null,则返回全部记录。
 */
export function searchRoles(
  pageNum: number,
  pageSize: number,
  params?: { searchInput?: string; pageNum: number; pageSize: number }
) {
  return request<RVOPageRoleRoleVO>(`/role/searchRoles/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查询角色记录
 * @description 按类型模糊查询角色数据记录。
 */
export function findRolesByType(params?: { searchInput: string }) {
  return request<RListRoleVO>(`/role/findRolesByType`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_23_02(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListRoleVO>(`/role/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_24_02(id: number, params?: { id: number }) {
  return request<RRoleVO>(`/role/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_2_02(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/role/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_23_02(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/role/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_24_02(id: number, params?: { id: number }) {
  return request<R>(`/role/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
