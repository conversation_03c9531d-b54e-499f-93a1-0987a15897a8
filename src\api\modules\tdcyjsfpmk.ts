/*
 * @OriginalName: 团队成员角色分配模块
 * @Description: 分配和调整团队成员的角色
 */
import { request } from '@/utils/request';

/**
 * 解除团队成员在团队中承担的角色
 * @description 根据用户Id和团队Id，解除团队成员在团队中承担的角色。
 */
export function deleteTeamUserRole(userId, data: TeamUserRoleDTO2) {
  return request<R>(`/teamUserRole/deleteTeamUserRole/${userId}`, {
    method: 'post',
    data,
  });
}

/**
 * 分配或更新团队成员在团队中承担的角色
 * @description 根据用户Id和团队Id，给团队成员分配角色或跟新角色分配信息。
 */
export function assignTeamUserRoles(data: TeamUserRoleDTO2) {
  return request<RListTeamUserRoleVO>(`/teamUserRole/assignTeamUserRoles`, {
    method: 'post',
    data,
  });
}

/**
 * 查看团队成员在团队中承担的角色
 * @description 根据用户Id和团队Id，查看团队成员在团队中承担的角色。
 */
export function findTeamUserRoleByTeamUserId(params?: { teamId: number; userId: number }) {
  return request<RListTeamUserRoleVO>(`/teamUserRole/findTeamUserRoleByTeamUserId`, {
    method: 'get',
    params,
  });
}
