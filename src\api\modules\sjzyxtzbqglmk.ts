/*
 * @OriginalName: 数据资源系统中标签管理模块
 * @Description: 标签的查询
 */
import { request } from '@/utils/request';

/**
 * 查找标签
 * @description 按动态条件，获取满足相应条件的标签。各条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findannotationByCriteria(data: DictionaryValueCriteria) {
  return request<RVOPage>(`/annotation/findannotationByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 按标签汇总统计文档数量
 * @description 根据组织结构字典内各字典值在文档记录表中出现的次数。
 */
export function findTotalAmountByDictionaryCodeOrg() {
  return request<RListDictionaryValueStatisticDTO>(`/annotation/findTotalAmountByDictionaryCodeOrg`, {
    method: 'get',
  });
}

/**
 * 按标签汇总统计文档数量
 * @description 根据疾病类型字典内各字典值在文档记录表中出现的次数。
 */
export function findTotalAmountByDictionaryCodeDisease() {
  return request<RListDictionaryValueStatisticDTO>(`/annotation/findTotalAmountByDictionaryCodeDisease`, {
    method: 'get',
  });
}

/**
 * 按标签汇总统计文档数量
 * @description 根据字典SystemDictionary的dictionaryCode，汇总统计该字典的各字典值在文档记录表中出现的次数。
 */
export function findTotalAmountBySystemDictionaryCode(dictionaryCode: string, params?: { dictionaryCode: string }) {
  return request<RListDictionaryValueStatisticDTO>(`/annotation/findTotalAmountByAnnotationId/${dictionaryCode}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找文档的标签
 * @description 查找文档的全部标签
 */
export function findDictionaryValueByFileInforId(
  fileInforId: number,
  pageNum: number,
  pageSize: number,
  params?: { fileInforId: number; pageNum: number; pageSize: number }
) {
  return request<RVOPageDictionaryValueDictionaryValueVO>(
    `/annotation/findAnnotationByFileInforId/${fileInforId}/${pageNum}/${pageSize}`,
    {
      method: 'get',
      params,
    }
  );
}

/**
 * 查找某个类别的标签
 * @description 查找某个类别的全部标签
 */
export function findDictionaryValueBySystemDictionaryId(
  annotationTypeId: number,
  pageNum: number,
  pageSize: number,
  params?: { annotationTypeId: number; pageNum: number; pageSize: number }
) {
  return request<RVOPageDictionaryValueDictionaryValueVO>(
    `/annotation/findAnnotationByAnnotationTypeId/${annotationTypeId}/${pageNum}/${pageSize}`,
    {
      method: 'get',
      params,
    }
  );
}
