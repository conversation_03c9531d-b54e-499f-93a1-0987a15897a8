/*
 * @OriginalName: 项目中的成员管理管理模块
 * @Description: 添加或移除参与项目的人员，设置参与人员的角色，查询项目的成员，查询人员参与的项目
 */
import { request } from '@/utils/request';

/**
 * 添加项目参与人员信息
 * @description 向项目中添加多个成员并添加该成员在项目中的角色。
 */
export function addApplicationUser(data: ApplicationUserDTO) {
  return request<RListApplicationUserVO>(`/ApplicationUser/addApplicationUser`, {
    method: 'post',
    data,
  });
}

/**
 * 添加项目参与人员信息
 * @description 开发人员使用，向项目中添加多个成员并添加该成员在项目中的角色。
 */
export function addApplicationUserDev(data: ApplicationUserDTO) {
  return request<RListApplicationUserVO>(`/ApplicationUser/addApplicationUserDev`, {
    method: 'post',
    data,
  });
}

/**
 * 移除项目参与人员
 * @description 从项目中移除多个成员。
 */
export function deleteApplicationUser(params?: { applicationId: number; userIds: Array<number> }) {
  return request<R>(`/ApplicationUser/removeApplicationUser`, {
    method: 'get',
    params,
  });
}

/**
 * 移除项目参与人员的角色
 * @description 移除项目参与人员在项目中的角色。
 */
export function removeApplicationUserRole(params?: { applicationId: number; userId: number; roleIds: Array<number> }) {
  return request<RApplicationUserVO>(`/ApplicationUser/removeApplicationUserRole`, {
    method: 'get',
    params,
  });
}

/**
 * 移除项目参与人员的角色
 * @description 开发人员使用，移除项目参与人员在项目中的角色。
 */
export function removeApplicationUserRoleDev(params?: {
  applicationId: number;
  userId: number;
  roleIds: Array<number>;
}) {
  return request<RApplicationUserVO>(`/ApplicationUser/removeApplicationUserRoleDev`, {
    method: 'get',
    params,
  });
}

/**
 * 查询项目参与人员信息
 * @description 查看用户参与的全部项目的信息。
 */
export function findApplicationUserByMdmUserId(params?: { userId: number }) {
  return request<RListApplicationUserVO>(`/ApplicationUser/findApplicationUserByMdmUserId`, {
    method: 'get',
    params,
  });
}

/**
 * 查询项目参与人员信息
 * @description 查看用户参与的全部项目的信息。
 */
export function findApplicationUserByMdmUserId_1(params?: { userId: number; pageNum: number; pageSize: number }) {
  return request<RPageApplicationUserVO>(`/ApplicationUser/findApplicationUserByMdmUserIdPage`, {
    method: 'get',
    params,
  });
}

/**
 * 查询项目参与人员信息
 * @description 查看项目申请中，全部成员的信息。
 */
export function findApplicationUserByApplicationId(params?: { applicationId: number }) {
  return request<RListApplicationUserVO>(`/ApplicationUser/findApplicationUserByApplicationId`, {
    method: 'get',
    params,
  });
}

/**
 * 查询项目参与人员信息
 * @description 根据项目申请的ID和用户ID，查找项目申请中，该成员的信息。
 */
export function findApplicationUserByApplicationIdAndMdmUserId(params?: { applicationId: number; userId: number }) {
  return request<RApplicationUserVO>(`/ApplicationUser/findApplicationUserByApplicationIdAndMdmUserId`, {
    method: 'get',
    params,
  });
}
