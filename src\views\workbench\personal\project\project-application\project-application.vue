<template>
  <div class="flex h-full flex-col">
    <div class="box-shadow bg-w pt-4 pl-10">
      <h2 v-if="!isEdit" class="text-xl font-bold">创建项目</h2>
      <h2 v-else class="relative text-xl font-bold" @click="onBack">
        <a>
          <div class="absolute top-[3px] left-[-22px]">
            <el-icon><ArrowLeft /></el-icon>
          </div>
          申请ID：{{ id }}
        </a>
      </h2>
      <el-tabs v-model="activeName" class="mt-4 select-none">
        <el-tab-pane label="申请信息" name="1" />
        <!-- <el-tab-pane label="付款信息" name="3" /> -->
        <template v-if="isEdit">
          <el-tab-pane label="团队信息" name="2" />
          <el-tab-pane label="项目成员" name="8" />
          <el-tab-pane label="更新需求" name="4" />
          <!-- <el-tab-pane label="过程管理" name="5" /> -->
          <!-- <el-tab-pane label="消息" name="6" /> -->
          <!-- <el-tab-pane label="数据" name="7" /> -->
        </template>
      </el-tabs>
    </div>

    <div class="h-0 flex-1">
      <ApplicationInfo v-if="activeName === '1'" :id="id" @get-state="onGetState" />
      <TeamInfo v-if="activeName === '2'" :id="id" :state="projectState" />
      <div v-if="activeName === '3'">付款信息</div>
      <DemandUpdating v-if="activeName === '4'" :id="id" :state="projectState" />
      <ProcessManage v-if="activeName === '5'" :id="id" :state="projectState" />
      <div v-if="activeName === '6'">消息</div>
      <Data v-if="activeName === '7'" :state="projectState" />
      <ProjectMembers v-if="activeName === '8'" :id="id" :state="projectState" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import ApplicationInfo from './components/ApplicationInfo.vue';
  import TeamInfo from './components/TeamInfo.vue';
  import ProjectMembers from './components/ProjectMembers.vue';
  import DemandUpdating from './components/DemandUpdating.vue';
  import ProcessManage from './components/ProcessManage/ProcessManage.vue';
  import Data from './components/Data.vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const props = defineProps({
    id: {
      type: String,
    },
  });
  const activeName = ref('1');
  const isEdit = computed(() => {
    return !['', null, undefined].includes(props.id);
  });

  // 项目当前状态
  const projectState = ref('');
  const onGetState = (state: string) => {
    projectState.value = state;
  };

  const onBack = () => {
    router.back();
  };
</script>

<style lang="scss" scoped>
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
</style>
