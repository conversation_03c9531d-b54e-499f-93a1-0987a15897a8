/*
 * @OriginalName: 权限管理模块
 * @Description: 管理系统中的各种权限
 */
import { request } from '@/utils/request';

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_4_02(data: PrivilegeDTO) {
  return request<RPrivilegeVO>(`/privilege/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_4_02(data: Array<number>) {
  return request<RListPrivilegeVO>(`/privilege/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_4_02(data: Array<number>) {
  return request<R>(`/privilege/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 分页查询全部权限记录
 * @description 按名字，代码或类型进行模糊查询，分页形式查询权限数据记录，若查询条件是null,则返回全部记录。
 */
export function searchPrivileges(
  pageNum: number,
  pageSize: number,
  params?: { searchInput?: string; pageNum: number; pageSize: number }
) {
  return request<RVOPagePrivilegePrivilegeVO>(`/privilege/searchPrivileges/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查询权限记录
 * @description 按类型模糊查询权限数据记录。
 */
export function findPrivilegesByType(params?: { searchInput: string }) {
  return request<RListPrivilegeVO>(`/privilege/findPrivilegesByType`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_27_02(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListPrivilegeVO>(`/privilege/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_28_02(id: number, params?: { id: number }) {
  return request<RPrivilegeVO>(`/privilege/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_4_02(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/privilege/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_27_02(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/privilege/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_28_02(id: number, params?: { id: number }) {
  return request<R>(`/privilege/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
