<template>
  <div class="bg-baf flex h-full flex-col overflow-hidden">
    <div v-loading="loading" class="bg-w m-5 flex h-0 flex-1 flex-col rounded-md pt-5">
      <div class="flex gap-4 px-10">
        <el-input
          v-model="userName"
          placeholder="用户名"
          style="width: 200px"
          clearable
          @clear="onSearch"
          @keyup.enter="onSearch"
        />
        <el-input
          v-model="name"
          placeholder="姓名"
          style="width: 200px"
          clearable
          @clear="onSearch"
          @keyup.enter="onSearch"
        />
        <div>
          <el-button type="default" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onAdd">新增</el-button>
        </div>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column prop="userName" label="用户名" />
          <el-table-column prop="rltSybole" label="角色">
            <template #default="{ row }">
              {{ roleText(row.roleVOList) }}
            </template>
          </el-table-column>
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="phone" label="手机号" />
          <!-- <el-table-column prop="email" label="邮箱" /> -->
          <el-table-column prop="userType" label="用户类型" />
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag v-if="row.rltSybole" :type="row.rltSybole.lockFlag === '0' ? 'success' : 'danger'">
                {{ userLockFlagText(row.rltSybole.lockFlag) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间">
            <template #default="{ row }">
              <div>{{ row.rltTime?.updateTime || '' }}</div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="140px">
            <template #default="{ row }">
              <el-tooltip content="编辑" effect="dark">
                <el-button link type="primary" icon="edit" @click="onEdit(row)" />
              </el-tooltip>
              <el-popconfirm title="确定删除？" @confirm="onDel(row)">
                <template #reference>
                  <span class="mx-3">
                    <el-tooltip content="删除" effect="dark">
                      <el-button link icon="delete" type="primary" />
                    </el-tooltip>
                  </span>
                </template>
              </el-popconfirm>
              <el-tooltip content="修改角色" effect="dark">
                <el-button link type="primary" icon="User" @click="onEditRole(row)" />
              </el-tooltip>
              <el-tooltip content="修改密码" effect="dark">
                <el-button link type="primary" icon="Lock" @click="onUpdatePwd(row)"></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <el-dialog v-model="showAdd" :title="formTitle" width="600px" @close="onAddClose">
      <el-form ref="formRef" :model="addForm" :rules="rules" label-width="140px" autocomplete="off">
        <el-form-item label="用户名：" prop="userName">
          <el-tooltip effect="light" placement="right-start">
            <template #content>
              <div class="text-tip w-[255px]">
                <div>长度为4-16位字符</div>
                <div class="mt-1">可使用英文、数字、下划线组合，不能只有数字或下划线，必须包含英文</div>
              </div>
            </template>
            <el-input
              v-model="addForm.userName"
              :disabled="addForm.id ? true : false"
              placeholder="请输入"
              maxlength="16"
            />
          </el-tooltip>
        </el-form-item>
        <el-form-item v-if="!addForm.id" label="密码" prop="password">
          <el-tooltip effect="light" placement="right-start" :visible="pwdVisible">
            <template #content>
              <div class="text-tip text-xs">
                <div class="mb-1">安全程度：{{ safeStrength }}</div>
                <el-progress :stroke-width="8" :percentage="pwdProgress" :show-text="false" />
                <ul class="mt-2 pl-5">
                  <li>最少8个字符</li>
                  <li>不能全为数字、字母或特殊符号</li>
                  <li>数字、字母、特殊字符任意组合</li>
                </ul>
              </div>
            </template>
            <el-input
              v-model="addForm.password"
              type="password"
              placeholder="请输入"
              maxlength="20"
              @input="onPwdInput"
              @focus="onPwdFocus"
              @blur="onPwdBlur"
            />
          </el-tooltip>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入" maxlength="20" />
        </el-form-item>
        <el-form-item label="类别" prop="userType">
          <el-select v-model="addForm.userType">
            <el-option v-for="item in userTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="邮箱：" prop="email">
          <el-input v-model="addForm.email" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="身份证号码" prop="idCard">
          <el-input v-model="addForm.idCard" placeholder="请输入身份证号码" maxlength="20" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="addForm.phone" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickName">
          <el-input v-model="addForm.nickName" placeholder="请输入" maxlength="10" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="addForm.rltSybole!.lockFlag">
            <el-option v-for="item in roleLockFlags" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="addForm.address" placeholder="请输入" maxlength="200" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span>
          <el-button @click="onAddClose">取消</el-button>
          <el-button type="primary" :loading="addLoading" @click="onAddConfirm"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>

    <el-drawer v-model="drawer2" class="relative" direction="rtl" destroy-on-close>
      <template #header>
        <h4 class="text-m">修改密码</h4>
      </template>
      <el-form ref="passFormRef" label-position="top" :model="passForm" :rules="passRules">
        <el-form-item label="新密码：" prop="newPass">
          <el-tooltip effect="light" placement="right-start" :visible="pwdVisible2">
            <template #content>
              <div class="text-tip text-xs">
                <div class="mb-1">安全程度：{{ safeStrength2 }}</div>
                <el-progress :stroke-width="8" :percentage="pwdProgress2" :show-text="false" />
                <ul class="mt-2 pl-5">
                  <li>最少8个字符</li>
                  <li>不能全为数字、字母或特殊符号</li>
                  <li>数字、字母、特殊字符任意组合</li>
                </ul>
              </div>
            </template>
            <el-input
              v-model="passForm.newPass"
              type="password"
              placeholder="请输入"
              maxlength="20"
              @input="onPwdInput2"
              @focus="onPwdFocus2"
              @blur="onPwdBlur2"
            />
          </el-tooltip>
        </el-form-item>
        <el-form-item label="确认新密码：" prop="confirmPass">
          <el-input v-model="passForm.confirmPass" type="password" placeholder="请再次输入新密码" maxlength="20" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="onCancelPass"> 取消 </el-button>
        <el-button color="#007f99" type="primary" :loading="passwordLoading" @click="onSavePass"> 保存 </el-button>
      </template>
    </el-drawer>

    <el-dialog v-model="showRole" title="修改角色" width="500px" @close="onRoleClose">
      <el-form ref="roleFormRef" :model="roleForm" :rules="roleRules">
        <el-form-item label="角色：" prop="roleId">
          <RoleSelect v-model="roleForm.roleId" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span>
          <el-button @click="onRoleClose">取消</el-button>
          <el-button type="primary" :loading="addLoading" @click="onRoleConfirm"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  // 导入API和工具函数
  import { deletePrivilegeById, assignRole2, findUserVOByCriteria, newOrUpdateUser } from '@/api';
  import { roleLockFlags, userTypes } from '@/utils/constants';
  import { usePasswordStrength } from '@/utils/form';
  import { roleText, userLockFlagText } from '@/utils/format';
  import { ElMessage, FormInstance } from 'element-plus';
  import { validateEmail, validatePassword, validatePhoneNumber, validateUsername } from '@/utils/validator';
  import { encryption } from '@/utils/crypto';
  import RoleSelect from '@/components/RoleSelect.vue';
  import { nextTick, ref, reactive, computed, onBeforeMount } from 'vue';

  // ==================== 类型定义 ====================
  type MDMUserDTOCustom = MDMUserDTO & {
    rltSybole: {
      lockFlag: string;
    };
  };

  // ==================== 密码强度相关 ====================
  // 新增用户密码强度
  const { pwdVisible, safeStrength, pwdProgress, onPwdInput, onPwdFocus, onPwdBlur } = usePasswordStrength();
  // 修改密码强度
  const {
    pwdVisible: pwdVisible2,
    safeStrength: safeStrength2,
    pwdProgress: pwdProgress2,
    onPwdInput: onPwdInput2,
    onPwdFocus: onPwdFocus2,
    onPwdBlur: onPwdBlur2,
  } = usePasswordStrength();

  // ==================== 公共状态 ====================
  const loading = ref(false);
  const currentRow = ref<MDMUserVO | null>(null);

  // ==================== 查询相关 ====================
  const userName = ref('');
  const name = ref('');

  const onSearch = () => {
    fetchData();
  };

  // ==================== 用户列表相关 ====================
  const tableData = ref<MDMUserVO[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);

  /**
   * 获取用户列表数据
   * @param pageNum 页码，默认为1
   */
  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      const { data } = await findUserVOByCriteria({
        name: name.value,
        userName: userName.value,
        pageNum,
        pageSize: pagination.pageSize,
      });
      total.value = data?.totalElement || 0;
      tableData.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 分页变化处理
   */
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };

  // ==================== 新增/编辑用户相关 ====================
  const formRef = ref<FormInstance>();
  const showAdd = ref(false);
  const addLoading = ref(false);

  // 表单数据
  const addForm = reactive<MDMUserDTOCustom>({
    id: 0,
    idCard: '',
    name: '',
    phone: '',
    email: '',
    address: '',
    userName: '',
    password: '',
    nickName: '',
    userType: '个人用户',
    rltSybole: {
      lockFlag: '0',
    },
  });

  // 表单校验规则
  const rules = ref({
    // idCard: [{ required: true, message: '不能为空' }],
    name: [{ required: true, message: '不能为空' }],
    userName: [{ required: true, validator: validateUsername }],
    password: [{ required: true, validator: validatePassword }],
    // phone: [{ required: true, validator: validatePhoneNumber }],
    // email: [{ required: true, validator: validateEmail }],
    // nickName: [{ required: true, message: '不能为空' }],
    userType: [{ required: true, message: '不能为空' }],
  });

  // 动态表单标题
  const formTitle = computed(() => (addForm.id ? '编辑账号' : '新增账号'));

  /**
   * 打开新增用户表单
   */
  const onAdd = () => {
    addForm.id = 0;
    addForm.address = '';
    addForm.rltSybole!.lockFlag = '0';
    showAdd.value = true;
  };

  /**
   * 打开编辑用户表单
   */
  const onEdit = (row: MDMUserVO) => {
    showAdd.value = true;
    nextTick(() => {
      addForm.id = row.id;
      addForm.idCard = row.idCard;
      addForm.name = row.name;
      addForm.phone = row.phone;
      addForm.email = row.email;
      addForm.address = row.address;
      addForm.nickName = row.nickName;
      addForm.userName = row.userName;
      addForm.userType = row.userType;
      addForm.rltSybole!.lockFlag = (row as any).rltSybole?.lockFlag;
    });
  };

  /**
   * 关闭新增/编辑表单
   */
  const onAddClose = () => {
    showAdd.value = false;
    formRef.value?.resetFields();
  };

  /**
   * 提交新增/编辑表单
   */
  const onAddConfirm = () => {
    formRef.value?.validate(async (valid) => {
      try {
        if (valid) {
          addLoading.value = true;
          let password = '';
          if (!addForm.id) {
            // 新增用户时加密密码
            password = encryption(addForm.password!, import.meta.env.VITE_PWD_ENC_KEY);
          }
          await newOrUpdateUser({ ...addForm, password: password === '' ? undefined : password });
          ElMessage({ type: 'success', message: '操作成功' });
          onAddClose();
          fetchData(pagination.page);
        }
      } catch (error) {
        console.log(error);
      } finally {
        addLoading.value = false;
      }
    });
  };

  // ==================== 删除用户相关 ====================
  /**
   * 删除用户
   */
  async function onDel(row: MDMUserVO) {
    try {
      loading.value = true;
      await deletePrivilegeById([row.id!]);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData(pagination.page);
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  // ==================== 修改密码相关 ====================
  const drawer2 = ref(false);
  const passFormRef = ref<FormInstance>();
  const passwordLoading = ref(false);

  // 密码表单数据
  let passForm = reactive({
    newPass: '',
    confirmPass: '',
  });

  // 密码表单校验规则
  let passRules = reactive({
    password: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
    newPass: [{ required: true, message: '请输入新密码', trigger: 'blur' }, { validator: validatePassword }],
    confirmPass: [
      { required: true, message: '请输入确认密码', trigger: 'blur' },
      { validator: validateConfirmPassword },
    ],
  });

  /**
   * 确认密码校验函数
   */
  function validateConfirmPassword(rule, value, callback) {
    if (value !== passForm.newPass) {
      callback(new Error('两次输入的密码不一致'));
    } else {
      callback();
    }
  }

  /**
   * 打开修改密码抽屉
   */
  const onUpdatePwd = (row: MDMUserVO) => {
    currentRow.value = row;
    passForm.newPass = '';
    passForm.confirmPass = '';
    drawer2.value = true;
  };

  /**
   * 保存新密码
   */
  const onSavePass = async () => {
    try {
      passwordLoading.value = true;
      await passFormRef.value?.validate();
      const newPass = encryption(passForm.newPass, import.meta.env.VITE_PWD_ENC_KEY);
      await newOrUpdateUser({
        id: currentRow.value?.id,
        password: newPass,
      });
      drawer2.value = false;
      ElMessage({ type: 'success', message: '修改密码成功' });
    } catch (error) {
      console.log(error);
    } finally {
      passwordLoading.value = false;
    }
  };

  /**
   * 取消修改密码
   */
  const onCancelPass = () => {
    passFormRef.value?.resetFields();
    drawer2.value = false;
  };

  // ==================== 修改角色相关 ====================
  const roleFormRef = ref<FormInstance>();
  const showRole = ref(false);

  // 角色表单数据
  const roleForm = reactive({
    roleId: [] as string[],
  });

  // 角色表单校验规则
  const roleRules = ref({
    roleId: [{ required: true, message: '不能为空' }],
  });

  /**
   * 打开修改角色对话框
   */
  const onEditRole = (row: MDMUserVO) => {
    currentRow.value = row;
    setTimeout(() => {
      if (row.roleVOList?.length) {
        console.log('🚀 ~ setTimeout ~ row.roleVOList:', row.roleVOList);
        roleForm.roleId = row.roleVOList.map((item) => item.id! + '');
      }
    }, 0);
    showRole.value = true;
  };

  /**
   * 关闭修改角色对话框
   */
  const onRoleClose = () => {
    roleFormRef.value?.resetFields();
    showRole.value = false;
  };

  /**
   * 提交修改角色
   */
  const onRoleConfirm = () => {
    roleFormRef.value?.validate(async (valid) => {
      try {
        if (valid) {
          addLoading.value = true;
          await assignRole2({
            mdmUserId: currentRow.value?.id,
            roleIds: roleForm.roleId.map((item) => +item),
          });
          ElMessage({ type: 'success', message: '修改角色成功' });
          onRoleClose();
          fetchData(pagination.page);
        }
      } catch (error) {
        console.log(error);
      } finally {
        addLoading.value = false;
      }
    });
  };

  // ==================== 生命周期钩子 ====================
  onBeforeMount(() => {
    fetchData();
  });
</script>
