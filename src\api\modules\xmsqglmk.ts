/*
 * @OriginalName: 项目申请管理模块
 * @Description: 项目申请的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 审核项目申请
 * @description 对状态为待审批的项目进行审核。审核结论必须为：审核通过/审核未通过
 */
export function verifyApplication(data: ApplicationProcessingDTO) {
  return request<RApplicationVO>(`/application/verifyApplication`, {
    method: 'post',
    data,
  });
}

/**
 * 更新项目申请
 * @description 按UpdateApplicationDTO的信息更新项目的信息。
 */
export function newOrUpdateApplication_02(userId: number, data: UpdateApplicationDTO, params?: { userId: number }) {
  return request<RApplicationVO>(`/application/updateApplication/${userId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 新建项目申请
 * @description 按ApplicationDTO的信息，新建或更新项目的信息。
 */
export function newOrUpdateApplication_1(userId: number, data: ApplicationDTO, params?: { userId: number }) {
  return request<RApplicationVO>(`/application/newOrUpdateApplication/${userId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 查找项目申请
 * @description 按动态条件，获取满足相应条件的项目的基本信息。如果有主键，按主键精确查找，若无主键，则其它条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findApplicationVOByCriteria(data: ApplicationCriteria) {
  return request<REntityVOPage>(`/application/findApplicationVOByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 查找审核结论
 * @description 项目申请被审核的结论。
 */
export function findVerifyResult_1(applicationId: number, params?: { applicationId: number }) {
  return request<RApplicationProcessingVO>(`/application/findVerifyResult/${applicationId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找项目申请中的订单
 * @description 按Application的Id，查找订单
 */
export function findOrderByApplicationId(
  applicationId: number,
  pageNum: number,
  pageSize: number,
  params?: { applicationId: number; pageNum: number; pageSize: number }
) {
  return request<RVOPage>(`/application/findOrderByApplicationId/${applicationId}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找项目申请中的医学数据集
 * @description 根据applicationId和数据集的属性，查找项目申请中，符合条件的全部医学数据集
 */
export function findFileInforByApplicationId(
  applicationId: number,
  pageNum: number,
  pageSize: number,
  params?: { applicationId: number; searchInput?: string; pageNum: number; pageSize: number }
) {
  return request<RVOPage>(`/application/findFileInforByApplicationId/${applicationId}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找项目申请
 * @description 按Application的Id，精确查找数据记录。
 */
export function findAppById(appId: number, params?: { appId: number }) {
  return request<RApplicationVO>(`/application/findAppById/${appId}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页显示完成审核的项目申请
 * @description 分页显示状态为“审核通过/审核未通过”的全部项目申请。
 */
export function findAllVerifyApplicationByStateIn(
  pageNum: number,
  pageSize: number,
  params?: { pageNum: number; pageSize: number }
) {
  return request<REntityVOPage>(`/application/findAllVerifyApplicationByStateIn/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页显示全部项目申请
 * @description 分页显示状态为“待审批”的全部项目申请。
 */
export function findAllVerifyApplication(
  pageNum: number,
  pageSize: number,
  params?: { pageNum: number; pageSize: number }
) {
  return request<REntityVOPage>(`/application/findAllVerifyApplication/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找项目申请
 * @description 按Application的Id，精确查找数据记录。
 */
export function findAllApplicationById(appId: Array<number>, params?: { appId: Array<number> }) {
  return request<RListApplicationVO>(`/application/findAllApplicationById/${appId}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页显示全部项目申请
 * @description 分页显示全部项目申请。
 */
export function findAllApplication(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<REntityVOPage>(`/application/findAllApplication/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除项目
 * @description 按Application的Id，删除一个或多个项目的记录。
 */
export function deleteApplicationById(applicationId: Array<number>, params?: { applicationId: Array<number> }) {
  return request<R>(`/application/deleteApplicationById/${applicationId}`, {
    method: 'get',
    params,
  });
}

/**
 * 提交项目申请
 * @description 将状态为“草稿”或“审核未通过”的项目提交审核。
 */
export function applyApplication(
  applicationId: number,
  userId: number,
  params?: { applicationId: number; userId: number }
) {
  return request<RApplicationVO>(`/application/applyApplication/${applicationId}/${userId}`, {
    method: 'get',
    params,
  });
}
