/*
 * @OriginalName: 数据资源系统中团队管理模块
 * @Description: 团队的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 从团队内移除用户
 * @description 按TeamUser的Id，删除一个或多个团队用户的关联记录.将一个或多个用户，从团队内移除。
 */
export function deleteTeamUserById(data: Array<TeamUserId>) {
  return request<R>(`/team/removeUserFromTeam`, {
    method: 'post',
    data,
  });
}

/**
 * 将团队退出机构
 * @description 按Team的Id，将一个或多个团队加入到机构中。
 */
export function removeTeamFromOrg(
  orgId: number,
  teamId: Array<number>,
  params?: { orgId: number; teamId: Array<number> }
) {
  return request<RListTeamVO>(`/team/removeTeamFromOrg/${orgId}/${teamId}`, {
    method: 'post',
    params,
  });
}

/**
 * 新建或更新团队
 * @description 按teamDTO的信息，新建或更新团队的信息。
 */
export function newOrUpdateTeam(data: TeamDTO) {
  return request<RTeamVO>(`/team/newOrUpdateTeam`, {
    method: 'post',
    data,
  });
}

/**
 * 查找团队
 * @description 按动态条件，获取满足相应条件的团队的基本信息。如果有主键，按主键精确查找，若无主键，则其它条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findOrgVOByCriteria(data: TeamCriteria) {
  return request<REntityVOPage>(`/team/findOrgVOByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 向团队内添加一个用户
 * @description 按teamUserDTO的信息，新建或更新团队用户关联的信息，向团队内添加一个用户。
 */
export function newOrUpdateTeamUser(data: TeamUserDTO) {
  return request<RTeamUserVO>(`/team/addUserToTeam`, {
    method: 'post',
    data,
  });
}

/**
 * 将团队加入机构
 * @description 按Team的Id，将一个或多个团队加入到机构中。
 */
export function addTeamToOrg(orgId: number, data: Array<number>, params?: { orgId: number }) {
  return request<RListTeamVO>(`/team/addTeamToOrg/${orgId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 将团队加入机构
 * @description 按Team的Id，将一个或多个团队加入到机构中。
 */
export function addTeamToOrg_1(
  orgId: number,
  teamId: Array<number>,
  params?: { orgId: number; teamId: Array<number> }
) {
  return request<RListTeamVO>(`/team/addTeamToOrg/${orgId}/${teamId}`, {
    method: 'post',
    params,
  });
}

/**
 * 查找可以加入团队的人员
 * @description 按Team的Id，查找可以加入团队的人员。
 */
export function findUserByIdNotIn_02(params?: {
  teamId: number;
  searchInput?: string;
  pageNum?: number;
  pageSize?: number;
}) {
  return request<RVOPage>(`/team/findUserByIdNotIn`, {
    method: 'get',
    params,
  });
}

/**
 * 查找团队中的成员在团队中的角色
 * @description 按Team的Id，查找团队的成员，以及成员所承担的角色。
 */
export function findTeamUserRoleVOByTeamId(teamId: number, params?: { teamId: number }) {
  return request<RListTeamUserRoleVO>(`/team/findTeamUserRoleVOByTeamId/${teamId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找团队
 * @description 按Team的Id，精确查找数据记录。
 */
export function findTeamById(teamId: number, params?: { teamId: number }) {
  return request<RTeamVO>(`/team/findTeamById/${teamId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找团队在参与的项目中的角色
 * @description 按Team的Id，查找团队在参与和主持的所有项目中，所承担的角色。
 */
export function findApplicationTeamRoleVOByTeamId(teamId: number, params?: { teamId: number }) {
  return request<RListApplicationTeamRoleVO>(`/team/findApplicationTeamRoleVOByTeamId/${teamId}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页显示全部团队
 * @description 分页显示全部团队。
 */
export function findAllTeam_02(params?: { searchInput?: string; pageNum?: number; pageSize?: number }) {
  return request<RVOPage>(`/team/findAllTeam`, {
    method: 'get',
    params,
  });
}

/**
 * 查找团队
 * @description 按Team的Id，精确查找数据记录。
 */
export function findAllTeamById_02(params?: { teamIds?: Array<number>; pageNum?: number; pageSize?: number }) {
  return request<RVOPage>(`/team/findAllTeamById`, {
    method: 'get',
    params,
  });
}

/**
 * 查找团队参与的项目
 * @description 按Team的Id，查找团队参与和主持的所有项目。
 */
export function findAllApplicationByTeamId(teamId: number, params?: { teamId: number }) {
  return request<RListApplicationTeamVO>(`/team/findAllApplicationByTeamId/${teamId}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除团队
 * @description 按Team的Id，删除一个或多个团队的记录。
 */
export function deleteTeamById(teamId: Array<number>, params?: { teamId: Array<number> }) {
  return request<R>(`/team/deleteTeamById/${teamId}`, {
    method: 'get',
    params,
  });
}
