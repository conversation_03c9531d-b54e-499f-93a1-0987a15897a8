<template>
  <div class="flex h-full flex-col">
    <h2 class="bg-w flex h-[60px] items-center pl-[28px] text-xl font-bold">项目管理</h2>

    <div class="bg-w m-5 flex h-0 flex-1 flex-col rounded-md pt-5">
      <div class="px-10">
        <el-button type="primary" @click="onApply"> 申请项目 </el-button>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table v-loading="loading" :data="tableData" class="c-table-header" height="100%">
          <el-table-column label="ID" prop="id" />
          <el-table-column label="项目名称" min-width="200px">
            <template #default="{ row }">
              <div class="flex items-center">
                <span>{{ row.title }}</span>

                <el-popover
                  v-if="['审核未通过'].includes(row.state)"
                  effect="light"
                  placement="bottom-start"
                  trigger="click"
                >
                  <template #reference>
                    <div class="audit-state" :style="`color: #E64545;`" @click="fetchOpinion(row)">
                      审核意见
                      <el-icon><CaretBottom /></el-icon>
                    </div>
                  </template>
                  <template #default>
                    <div v-loading="opinionLoading" class="text-xs">
                      <div class="text-tip">审核意见：</div>
                      {{ opinion }}
                    </div>
                  </template>
                </el-popover>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="申请时间">
            <template #default="{ row }">
              <span>{{ dayjs(row.createTime).format('YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="结束时间">
            <template #default="{ row }">
              <EndTimeDisplay :create-time="row.createTime" :duration="row.duration" />
            </template>
          </el-table-column>
          <el-table-column label="状态">
            <template #default="{ row }">
              <span class="status" :class="applicationStatusClass[row.state]">{{ row.state }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240">
            <template #default="{ row }">
              <el-button link type="primary" @click="onView(row)"> 编辑 </el-button>
              <!-- v-if="[null, '', '审核未通过'].includes(row.state)" -->
              <el-popconfirm title="确定提交申请？" @confirm="onSubmit(row)">
                <template #reference>
                  <el-button link type="primary"> 提交申请 </el-button>
                </template>
              </el-popconfirm>
              <!-- <el-button link type="primary" @click="onDocument(row)">文档</el-button> -->
              <!-- v-if="[null, '', '审核未通过'].includes(row.state)" -->
              <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                <template #reference>
                  <el-button link type="primary"> 删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  /* 项目管理 */
  import EndTimeDisplay from '../../components/EndTimeDisplay.vue';
  import dayjs from 'dayjs';
  import { ElMessage } from 'element-plus';
  import {
    findAllApplication,
    deleteApplicationById,
    applyApplication,
    findVerifyResult,
    findVerifyResult_1,
  } from '@/api/index';
  import { CaretBottom } from '@element-plus/icons-vue';
  // import { getpersonAllProject } from '@/api/person/person_project/index';
  import { useRouter } from 'vue-router';
  import { useUsers } from '@/store/user-info.js';
  import { applicationStatusClass } from '@/utils/constants';
  const router = useRouter();
  const store = useUsers();

  const tableData = ref([
    // {
    //   aid: '4563223584',
    //   title: '脑疾病研究脑疾病研究',
    //   head: '项目负责人',
    //   status: 0,
    //   auditState: 0,
    //   finalTime: '2023-12-28 15:21:15',
    // },
  ]);
  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
    fetchData();
  };

  //查看编辑
  const onView = (row) => {
    router.push({ name: 'PersonalProjectEdit', query: { id: row.id } });
  };

  //提交申请
  const onSubmit = async (row) => {
    try {
      loading.value = true;
      await applyApplication(row.id, store.user.id, {} as any);
      ElMessage({ type: 'success', message: '提交成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };
  //文档
  const onDocument = (row) => {
    //TODO
  };
  const getStateText = (row) => {
    let className = '';
    switch (row.auditState) {
      case 0:
        className = '审核中';
        break;
      case 1:
        className = '通过';
        break;
      case 2:
        className = '驳回';
        break;
      case 3:
        className = '申报中';
        break;
    }
    return className;
  };
  const disabled = ref(true);
  const getStateColor = (row) => {
    let className = '';
    switch (row.state) {
      // case '审核未通过':
      //   className = '#3A73E6';
      //   break;
      // case '审核未通过':
      //   className = '#24B383';
      //   break;
      case '审核未通过':
        className = '#E64545';
        break;
      case 3:
        className = '#939899';
        break;
    }
    return className;
  };
  const statusText = {
    0: '待启动',
    1: '进行中',
    2: '已结束',
    3: '申报中',
  };

  //申请项目
  const onApply = () => {
    router.push({ name: 'PersonalProjectApplication' });
  };

  fetchData();
  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      const { data } = await findAllApplication(pageNum, pagination.pageSize, {} as any);
      total.value = data?.totalElement || 0;
      pagination.page = pageNum;
      tableData.value = data?.content || ([] as any);
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
  const onDel = async (row) => {
    try {
      loading.value = true;
      await deleteApplicationById(row.id, {} as any);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  const opinion = ref('');
  const opinionLoading = ref(false);
  const fetchOpinion = async (row) => {
    try {
      opinionLoading.value = true;
      opinion.value = '';
      const { data } = await findVerifyResult_1(row.id);
      opinion.value = data?.description ?? '暂无';
    } catch (error) {
      console.log(error);
    } finally {
      opinionLoading.value = false;
    }
  };
</script>

<style lang="scss" scoped>
  .status-text::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 6px;
    margin-right: 6px;
    position: relative;
    top: -2px;
  }

  .status-to-start::before {
    background: #e6a117;
  }

  .status-under-way::before {
    background: #24b383;
  }

  .status-finished::before {
    background: #939899;
  }

  .audit-state {
    text-align: center;
    border-width: 1px;
    border-style: solid;
    border-radius: 12px;
    margin-left: 12px;
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    padding: 0 8px;
    cursor: pointer;
  }
</style>
